# Cloudflare Workers 订阅代理服务

这是一个基于 Cloudflare Workers 的订阅地址代理服务，支持 IP 白名单功能。

## 功能特性

- 🔒 **IP 白名单保护** - 只有白名单中的 IP 才能访问代理地址
- 🌐 **订阅地址代理** - 将原始订阅地址转换为代理地址
- 🛠️ **管理界面** - 简单易用的 Web 管理界面
- ⚡ **高性能** - 基于 Cloudflare Workers 的全球边缘计算
- 🎨 **美观UI界面** - 提供现代化的状态检查页面，支持响应式设计
- 📱 **移动友好** - 完美适配手机和平板设备
- 🔄 **实时刷新** - 状态页面支持实时刷新和自动更新
- 📊 **详细信息** - 显示完整的白名单状态和IP列表

## 部署步骤

### 1. 安装依赖

```bash
npm install
```

### 2. 配置 Cloudflare

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 创建一个新的 KV 命名空间：
   - 进入 Workers & Pages > KV
   - 点击 "Create a namespace"
   - 命名为 `subscription-proxy-kv`
3. 复制 KV 命名空间 ID 到 `wrangler.toml` 文件中

### 3. 修改配置

编辑 `wrangler.toml` 文件：

```toml
[[kv_namespaces]]
binding = "SUBSCRIPTION_KV"
id = "你的KV命名空间ID"
preview_id = "你的预览KV命名空间ID"

[vars]
ADMIN_PASSWORD = "你的管理员密码"
```

### 4. 部署到 Cloudflare Workers

```bash
# 开发模式
npm run dev

# 部署到生产环境
npm run deploy
```

## 使用方法

### 1. 访问管理界面

部署完成后，访问 `https://your-worker.your-subdomain.workers.dev/admin`

### 2. 添加订阅地址

1. 在管理界面输入管理员密码
2. 输入原始订阅地址（例如：`https://wd-red.com/subscribe/djkbm-mji2hi93`）
3. 点击 "Create Proxy" 创建代理

系统会生成：
- **代理地址**：`https://your-worker.your-subdomain.workers.dev/proxy/随机ID`
- **白名单地址**：`https://your-worker.your-subdomain.workers.dev/api/whitelist/随机ID?action=add`
- **状态检查页面（UI）**：`https://your-worker.your-subdomain.workers.dev/status/随机ID`
- **状态检查地址（JSON）**：`https://your-worker.your-subdomain.workers.dev/api/whitelist/随机ID`

### 3. 添加 IP 到白名单

有两种方式添加 IP 到白名单：

#### 方式一：通过管理界面
1. 在管理界面的 "Add IP to Whitelist" 部分
2. 输入代理 ID 和要添加的 IP 地址
3. 点击 "Add IP"

#### 方式二：通过白名单 URL
用户直接访问白名单地址（POST 请求），系统会自动将访问者的 IP 添加到白名单：

```bash
curl -X POST https://your-worker.your-subdomain.workers.dev/api/whitelist/代理ID
```

### 4. 查看白名单状态

#### 使用状态检查UI页面（推荐）
访问 `https://your-worker.your-subdomain.workers.dev/status/代理ID`

状态检查页面提供：
- 🎨 美观的现代化界面
- 📊 当前IP的白名单状态显示
- 📝 完整的白名单IP列表
- 🔄 一键刷新功能
- ➕ 快速添加IP按钮（如果当前IP不在白名单中）
- 📱 响应式设计，支持手机和平板

#### 使用JSON接口
访问 `https://your-worker.your-subdomain.workers.dev/api/whitelist/代理ID`

返回JSON格式的状态信息，适合程序调用。

### 5. 使用代理地址

只有白名单中的 IP 才能访问代理地址。访问代理地址时，系统会：
1. 检查访问者 IP 是否在白名单中
2. 如果在白名单中，则代理请求到原始订阅地址
3. 如果不在白名单中，返回 403 错误

## API 接口

### 检查 IP 白名单状态

#### JSON 接口
```bash
GET /api/whitelist/{proxy_id}
```

返回：
```json
{
  "whitelisted": true,
  "ip": "*******",
  "proxy_id": "代理ID",
  "total_ips": 5,
  "all_ips": ["*******", "*******"]
}
```

#### UI 界面
```bash
GET /status/{proxy_id}
```

提供美观的网页界面，显示：
- 当前IP的白名单状态
- 所有白名单IP列表
- 实时刷新功能
- 快速添加IP按钮
- 响应式设计，支持移动设备

### 添加当前 IP 到白名单

```bash
GET /api/whitelist/{proxy_id}?action=add
```

返回：
```json
{
  "success": true,
  "message": "IP ******* 已成功添加到代理 xxx 的白名单",
  "ip": "*******",
  "proxy_id": "代理ID",
  "total_ips": 6
}
```

## 安全说明

- 管理员密码存储在环境变量中，请设置强密码
- 所有数据存储在 Cloudflare KV 中，具有高可用性
- IP 白名单检查基于 `CF-Connecting-IP` 头部，确保获取真实客户端 IP

## 故障排除

1. **KV 命名空间错误**：确保在 `wrangler.toml` 中正确配置了 KV 命名空间 ID
2. **权限错误**：检查管理员密码是否正确设置
3. **代理失败**：检查原始订阅地址是否可访问

## 许可证

MIT License
