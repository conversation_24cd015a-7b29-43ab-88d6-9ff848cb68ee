// Cloudflare Workers 订阅代理服务
// 支持IP白名单和订阅地址管理

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // 获取客户端IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    request.headers.get('X-Real-IP') || 
                    '127.0.0.1';

    // 路由处理
    if (path === '/' || path === '/admin') {
      return handleHome(request, env);
    } else if (path.startsWith('/api/')) {
      return handleAPI(request, env, clientIP);
    } else if (path.startsWith('/proxy/')) {
      return handleProxy(request, env, clientIP);
    } else if (path.startsWith('/status/')) {
      return handleStatus(request, env, clientIP);
    } else if (path === '/whitelist-helper') {
      return handleWhitelistHelper(request);
    }
    
    return new Response('页面未找到', { status: 404 });
  }
};

// 处理首页
async function handleHome(request, env) {
  if (request.method === 'GET') {
    return new Response(getHomeHTML(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
  
  if (request.method === 'POST') {
    const formData = await request.formData();
    const action = formData.get('action');

    if (action === 'create_proxy') {
      const originalUrl = formData.get('original_url');

      if (!originalUrl) {
        return new Response(JSON.stringify({
          success: false,
          error: '订阅地址不能为空'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const proxyId = generateId();
      const proxyPassword = generatePassword();

      // 存储订阅地址和管理密码
      await env.SUBSCRIPTION_KV.put(`sub:${proxyId}`, originalUrl);
      await env.SUBSCRIPTION_KV.put(`pwd:${proxyId}`, proxyPassword);

      const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
      const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}?action=add`;
      const checkUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;
      const statusUrl = `${new URL(request.url).origin}/status/${proxyId}`;

      return new Response(JSON.stringify({
        success: true,
        proxy_url: proxyUrl,
        whitelist_url: whitelistUrl,
        check_url: checkUrl,
        status_url: statusUrl,
        proxy_id: proxyId,
        proxy_password: proxyPassword
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (action === 'manage_proxy') {
      const proxyId = formData.get('proxy_id');
      const proxyPassword = formData.get('proxy_password');
      const subAction = formData.get('sub_action');

      if (!proxyId || !proxyPassword) {
        return new Response(JSON.stringify({
          success: false,
          error: '代理ID和密码不能为空'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 验证代理密码
      const storedPassword = await env.SUBSCRIPTION_KV.get(`pwd:${proxyId}`);
      if (!storedPassword || storedPassword !== proxyPassword) {
        return new Response(JSON.stringify({
          success: false,
          error: '代理ID或密码错误'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      if (subAction === 'add_ip') {
        const ip = formData.get('ip_address');

        if (!ip) {
          return new Response(JSON.stringify({
            success: false,
            error: 'IP地址不能为空'
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }

        try {
          const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
          const ipList = existingIPs ? JSON.parse(existingIPs) : [];

          if (!ipList.includes(ip)) {
            ipList.push(ip);
            await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
          }

          return new Response(JSON.stringify({
            success: true,
            message: `IP ${ip} 已成功添加到代理 ${proxyId} 的白名单`,
            ip: ip,
            proxy_id: proxyId,
            total_ips: ipList.length
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: `添加IP失败: ${error.message}`
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }

      if (subAction === 'get_info') {
        try {
          const originalUrl = await env.SUBSCRIPTION_KV.get(`sub:${proxyId}`);
          const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
          const ipList = existingIPs ? JSON.parse(existingIPs) : [];

          const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
          const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}?action=add`;
          const checkUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;
          const statusUrl = `${new URL(request.url).origin}/status/${proxyId}`;

          return new Response(JSON.stringify({
            success: true,
            proxy_id: proxyId,
            original_url: originalUrl,
            proxy_url: proxyUrl,
            whitelist_url: whitelistUrl,
            check_url: checkUrl,
            status_url: statusUrl,
            whitelist_ips: ipList,
            total_ips: ipList.length
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        } catch (error) {
          return new Response(JSON.stringify({
            success: false,
            error: `获取信息失败: ${error.message}`
          }), {
            headers: { 'Content-Type': 'application/json' }
          });
        }
      }
    }
  }
  
  return new Response('不允许的请求方法', { status: 405 });
}

// 处理API请求
async function handleAPI(request, env, clientIP) {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  
  if (pathParts[2] === 'whitelist' && pathParts[3]) {
    const proxyId = pathParts[3];
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (request.method === 'POST' || action === 'add') {
      // 添加当前IP到白名单
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(clientIP)) {
          ipList.push(clientIP);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        // 如果是GET请求且action=add，返回用户友好的HTML页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPSuccessHTML(clientIP, proxyId, ipList.length), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        // 否则返回JSON响应
        return new Response(JSON.stringify({
          success: true,
          message: `IP ${clientIP} 已成功添加到代理 ${proxyId} 的白名单`,
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        // 如果是GET请求且action=add，返回用户友好的错误页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPErrorHTML(clientIP, proxyId, error.message), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        return new Response(JSON.stringify({
          success: false,
          error: `添加IP到白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    if (request.method === 'GET' && action !== 'add') {
      // 检查IP是否在白名单中
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        return new Response(JSON.stringify({
          whitelisted: ipList.includes(clientIP),
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length,
          all_ips: ipList // 显示所有白名单IP用于调试
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          error: `检查白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('API接口未找到', { status: 404 });
}

// 处理代理请求
async function handleProxy(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];
  
  if (!proxyId) {
    return new Response('无效的代理ID', { status: 400 });
  }
  
  // 检查IP白名单
  const whitelistData = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
  const whitelist = whitelistData ? JSON.parse(whitelistData) : [];
  
  if (!whitelist.includes(clientIP)) {
    return new Response('访问被拒绝：IP不在白名单中', { status: 403 });
  }
  
  // 获取原始订阅地址
  const originalUrl = await env.SUBSCRIPTION_KV.get(`sub:${proxyId}`);
  
  if (!originalUrl) {
    return new Response('订阅地址未找到', { status: 404 });
  }
  
  try {
    // 代理请求到原始地址
    const response = await fetch(originalUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    // 返回代理响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    return new Response('代理错误: ' + error.message, { status: 500 });
  }
}

// 处理状态检查页面
async function handleStatus(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];

  if (!proxyId) {
    return new Response('无效的代理ID', { status: 400 });
  }

  // 检查代理是否存在
  const originalUrl = await env.SUBSCRIPTION_KV.get(`sub:${proxyId}`);
  if (!originalUrl) {
    return new Response(getStatusNotFoundHTML(proxyId), {
      headers: { 'Content-Type': 'text/html' }
    });
  }

  // 获取白名单状态
  try {
    const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
    const ipList = existingIPs ? JSON.parse(existingIPs) : [];
    const isWhitelisted = ipList.includes(clientIP);

    return new Response(getStatusHTML(proxyId, clientIP, isWhitelisted, ipList), {
      headers: { 'Content-Type': 'text/html' }
    });
  } catch (error) {
    return new Response(getStatusErrorHTML(proxyId, clientIP, error.message), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

// 处理白名单助手页面
async function handleWhitelistHelper(request) {
  return new Response(getWhitelistHelperHTML(), {
    headers: { 'Content-Type': 'text/html' }
  });
}

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// 生成随机密码
function generatePassword() {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

// 获取首页HTML
function getHomeHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>订阅代理服务</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5em;
        }

        .card-icon.add { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-icon.ip { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .card-icon.view { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

        .card h2 {
            color: #333;
            font-size: 1.5em;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 0.95em;
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .result p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .result a {
            color: inherit;
            text-decoration: none;
            font-weight: 600;
            border-bottom: 1px dashed currentColor;
        }

        .result a:hover {
            border-bottom-style: solid;
        }

        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .info-box strong {
            color: #1976d2;
        }

        /* 模式切换样式 */
        .mode-switcher {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            background: white;
            border-radius: 15px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .mode-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            background: transparent;
            color: #666;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .mode-btn:hover:not(.active) {
            background: #f5f5f5;
            color: #333;
        }

        /* 标签页样式 */
        .manage-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .tab-btn {
            padding: 12px 20px;
            border: none;
            background: transparent;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-btn:hover:not(.active) {
            color: #333;
            background: #f5f5f5;
        }

        .tab-content {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 小字体提示 */
        small {
            display: block;
            margin-top: 5px;
            color: #666;
            font-size: 0.85em;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 订阅代理服务</h1>
            <p>创建和管理您的订阅代理，支持IP白名单保护</p>
        </div>

        <div class="content">
            <!-- 模式切换 -->
            <div class="mode-switcher">
                <button class="mode-btn active" onclick="switchMode('create')">🆕 创建新代理</button>
                <button class="mode-btn" onclick="switchMode('manage')">🛠️ 管理现有代理</button>
            </div>

            <!-- 创建代理模式 -->
            <div id="createMode" class="card">
                <div class="card-header">
                    <div class="card-icon add">➕</div>
                    <h2>创建新代理</h2>
                </div>
                <form id="createProxyForm">
                    <div class="form-group">
                        <label>🔗 原始订阅地址</label>
                        <input type="url" id="originalUrl" placeholder="https://example.com/subscribe/your-subscription" required>
                        <small>输入您要代理的订阅地址</small>
                    </div>
                    <button type="submit" class="btn">创建代理</button>
                </form>
                <div id="createResult"></div>
            </div>

            <!-- 管理代理模式 -->
            <div id="manageMode" class="card" style="display: none;">
                <div class="card-header">
                    <div class="card-icon ip">🛠️</div>
                    <h2>管理现有代理</h2>
                </div>

                <!-- 登录表单 -->
                <div id="loginForm">
                    <form id="proxyLoginForm">
                        <div class="form-group">
                            <label>🆔 代理ID</label>
                            <input type="text" id="manageProxyId" placeholder="请输入代理ID" required>
                        </div>
                        <div class="form-group">
                            <label>🔐 代理密码</label>
                            <input type="password" id="manageProxyPassword" placeholder="请输入代理密码" required>
                            <small>创建代理时生成的管理密码</small>
                        </div>
                        <button type="submit" class="btn">登录管理</button>
                    </form>
                </div>

                <!-- 管理界面 -->
                <div id="manageInterface" style="display: none;">
                    <div class="manage-tabs">
                        <button class="tab-btn active" onclick="switchTab('info')">📊 代理信息</button>
                        <button class="tab-btn" onclick="switchTab('addip')">➕ 添加IP</button>
                    </div>

                    <!-- 代理信息标签 -->
                    <div id="infoTab" class="tab-content">
                        <div id="proxyInfo"></div>
                    </div>

                    <!-- 添加IP标签 -->
                    <div id="addipTab" class="tab-content" style="display: none;">
                        <form id="addIPForm">
                            <div class="form-group">
                                <label>📍 IP地址</label>
                                <input type="text" id="ipAddress" placeholder="例如: ***********" required>
                            </div>
                            <button type="submit" class="btn">添加IP到白名单</button>
                        </form>
                        <div id="addIPResult"></div>
                    </div>
                </div>

                <div id="manageResult"></div>
            </div>


        </div>
    </div>

    <script>
        // 添加加载动画
        function showLoading(buttonElement) {
            const originalText = buttonElement.textContent;
            buttonElement.textContent = '处理中...';
            buttonElement.disabled = true;
            return originalText;
        }

        function hideLoading(buttonElement, originalText) {
            buttonElement.textContent = originalText;
            buttonElement.disabled = false;
        }

        // 模式切换功能
        function switchMode(mode) {
            const createBtn = document.querySelector('.mode-btn:first-child');
            const manageBtn = document.querySelector('.mode-btn:last-child');
            const createMode = document.getElementById('createMode');
            const manageMode = document.getElementById('manageMode');

            if (mode === 'create') {
                createBtn.classList.add('active');
                manageBtn.classList.remove('active');
                createMode.style.display = 'block';
                manageMode.style.display = 'none';
            } else {
                createBtn.classList.remove('active');
                manageBtn.classList.add('active');
                createMode.style.display = 'none';
                manageMode.style.display = 'block';
            }
        }

        // 标签页切换功能
        function switchTab(tab) {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.style.display = 'none');

            document.querySelector(\`[onclick="switchTab('\${tab}')"]\`).classList.add('active');
            document.getElementById(\`\${tab}Tab\`).style.display = 'block';
        }

        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        // 创建代理表单处理
        document.getElementById('createProxyForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'create_proxy');
            formData.append('original_url', document.getElementById('originalUrl').value);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('createResult').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 代理创建成功！</h3>
                            <p><strong>🆔 代理ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code>
                               <button onclick="copyToClipboard('\${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔐 管理密码：</strong> <code style="background: #fff3cd; padding: 2px 6px; border-radius: 4px; font-family: monospace; color: #856404;">\${result.proxy_password}</code>
                               <button onclick="copyToClipboard('\${result.proxy_password}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🔗 代理地址：</strong> <a href="\${result.proxy_url}" target="_blank">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>✨ 白名单地址（用户使用）：</strong> <a href="\${result.whitelist_url}" target="_blank">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🎨 状态检查页面（UI）：</strong> <a href="\${result.status_url}" target="_blank">\${result.status_url}</a>
                               <button onclick="copyToClipboard('\${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <div class="info-box">
                                <strong>⚠️ 重要提醒：</strong><br>
                                • <strong>请保存管理密码：</strong> 用于管理此代理的白名单<br>
                                • <strong>分享白名单地址：</strong> 用户点击即可自动添加IP到白名单<br>
                                • <strong>状态检查页面：</strong> 提供美观的白名单状态查看界面
                            </div>
                        </div>
                    \`;
                } else {
                    document.getElementById('createResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 创建代理失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                            <p>请检查订阅地址是否正确，或稍后重试。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('createResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        // 代理登录表单处理
        document.getElementById('proxyLoginForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'manage_proxy');
            formData.append('sub_action', 'get_info');
            formData.append('proxy_id', document.getElementById('manageProxyId').value);
            formData.append('proxy_password', document.getElementById('manageProxyPassword').value);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    // 隐藏登录表单，显示管理界面
                    document.getElementById('loginForm').style.display = 'none';
                    document.getElementById('manageInterface').style.display = 'block';

                    // 显示代理信息
                    document.getElementById('proxyInfo').innerHTML = \`
                        <div class="result success">
                            <h3>📊 代理信息</h3>
                            <p><strong>🆔 代理ID：</strong> <code>\${result.proxy_id}</code></p>
                            <p><strong>🔗 原始订阅地址：</strong> <a href="\${result.original_url}" target="_blank">\${result.original_url}</a></p>
                            <p><strong>🚀 代理地址：</strong> <a href="\${result.proxy_url}" target="_blank">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>✨ 白名单地址：</strong> <a href="\${result.whitelist_url}" target="_blank">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🎨 状态检查页面：</strong> <a href="\${result.status_url}" target="_blank">\${result.status_url}</a>
                               <button onclick="copyToClipboard('\${result.status_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>📊 白名单IP数量：</strong> \${result.total_ips} 个</p>
                            \${result.whitelist_ips.length > 0 ? \`
                                <div style="margin-top: 15px;">
                                    <strong>📝 白名单IP列表：</strong>
                                    <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 5px; max-height: 150px; overflow-y: auto;">
                                        \${result.whitelist_ips.map((ip, index) => \`<div>\${index + 1}. \${ip}</div>\`).join('')}
                                    </div>
                                </div>
                            \` : '<p style="color: #666; margin-top: 10px;">暂无白名单IP</p>'}
                        </div>
                    \`;
                } else {
                    document.getElementById('manageResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 登录失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                            <p>请检查代理ID和密码是否正确。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('manageResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        // 添加IP表单处理
        document.getElementById('addIPForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'manage_proxy');
            formData.append('sub_action', 'add_ip');
            formData.append('proxy_id', document.getElementById('manageProxyId').value);
            formData.append('proxy_password', document.getElementById('manageProxyPassword').value);
            formData.append('ip_address', document.getElementById('ipAddress').value);

            try {
                const response = await fetch('/', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('addIPResult').innerHTML = \`
                        <div class="result success">
                            <h3>✅ IP添加成功！</h3>
                            <p><strong>📍 IP地址：</strong> <code>\${result.ip}</code></p>
                            <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            <p>该IP现在可以访问代理地址了。</p>
                        </div>
                    \`;
                    // 清空输入框
                    document.getElementById('ipAddress').value = '';
                } else {
                    document.getElementById('addIPResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 添加IP失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('addIPResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });


    </script>
</body>
</html>`;
}

// 获取添加IP成功页面HTML
function getAddIPSuccessHTML(ip, proxyId, totalIPs) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>IP添加成功</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            animation: slideUp 0.6s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .info-card {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #28a745;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #155724;
            min-width: 120px;
        }

        .info-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }

        .proxy-link {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .proxy-link a {
            color: #1976d2;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
        }

        .proxy-link a:hover {
            text-decoration: underline;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .info-label {
                margin-bottom: 5px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 IP添加成功！</h1>
        </div>

        <div class="content">
            <div class="info-card">
                <div class="info-item">
                    <span class="info-label">📍 您的IP地址：</span>
                    <span class="info-value">${ip}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">🆔 代理ID：</span>
                    <span class="info-value">${proxyId}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">📊 白名单总数：</span>
                    <span class="info-value">${totalIPs} 个IP</span>
                </div>
            </div>

            <div class="proxy-link">
                <p style="margin-bottom: 10px; color: #1976d2; font-weight: 600;">🚀 您现在可以访问代理地址：</p>
                <a href="/proxy/${proxyId}" target="_blank">/proxy/${proxyId}</a>
            </div>

            <div class="button-group">
                <a href="/api/whitelist/${proxyId}" class="btn">📋 查看白名单状态</a>
                <a href="/whitelist-helper" class="btn secondary">🛠️ 白名单助手</a>
            </div>
        </div>
    </div>
</body>
</html>`;
}

// 获取添加IP错误页面HTML
function getAddIPErrorHTML(ip, proxyId, errorMessage) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>添加IP错误</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            animation: slideUp 0.6s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .error-card {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #dc3545;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #dc3545;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #721c24;
            min-width: 120px;
        }

        .info-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
            font-weight: 500;
        }

        .help-text {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #856404;
            text-align: center;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.retry {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.retry:hover {
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .info-label {
                margin-bottom: 5px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ 添加IP失败</h1>
        </div>

        <div class="content">
            <div class="error-card">
                <div class="info-item">
                    <span class="info-label">📍 您的IP地址：</span>
                    <span class="info-value">${ip}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">🆔 代理ID：</span>
                    <span class="info-value">${proxyId}</span>
                </div>
            </div>

            <div class="error-message">
                <strong>🚨 错误详情：</strong> ${errorMessage}
            </div>

            <div class="help-text">
                <p><strong>💡 建议解决方案：</strong></p>
                <p>• 检查代理ID是否正确</p>
                <p>• 稍后重试</p>
                <p>• 联系管理员获取帮助</p>
            </div>

            <div class="button-group">
                <a href="/api/whitelist/${proxyId}?action=add" class="btn retry">🔄 重试</a>
                <a href="/whitelist-helper" class="btn secondary">🛠️ 白名单助手</a>
            </div>
        </div>
    </div>
</body>
</html>`;
}

// 获取白名单助手页面HTML
function getWhitelistHelperHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>白名单助手</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .input-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
        }

        .btn {
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-block;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.primary:hover {
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .btn.info:hover {
            box-shadow: 0 10px 25px rgba(23, 162, 184, 0.4);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result.info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .info-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .info-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-section p {
            margin-bottom: 10px;
            line-height: 1.6;
            color: #1976d2;
        }

        .info-section code {
            background: rgba(25, 118, 210, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            color: #1565c0;
        }

        .info-section ul {
            margin-left: 20px;
            color: #1976d2;
        }

        .info-section li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .input-card {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 白名单助手</h1>
            <p>轻松管理您的IP白名单，一键添加或查看状态</p>
        </div>

        <div class="content">
            <div class="input-card">
                <div class="form-group">
                    <label>🆔 代理ID</label>
                    <input type="text" id="proxyId" placeholder="请输入您的代理ID" required>
                </div>

                <div class="button-group">
                    <button onclick="addCurrentIP()" class="btn primary">➕ 将我的IP添加到白名单</button>
                    <button onclick="checkWhitelistStatus()" class="btn secondary">🔍 检查白名单状态</button>
                    <button onclick="generateWhitelistURL()" class="btn info">🔗 生成白名单链接</button>
                </div>

                <div id="result"></div>
            </div>

            <div class="info-section">
                <h3>🚀 简单方法 - 直接访问链接</h3>
                <p>您也可以通过在浏览器中访问此链接来添加您的IP：</p>
                <p><code>/api/whitelist/您的代理ID?action=add</code></p>
                <p>只需将"您的代理ID"替换为您的实际代理ID。</p>

                <h3>📖 使用说明</h3>
                <ul>
                    <li>在上方字段中输入您的代理ID</li>
                    <li>点击"生成白名单链接"获取直接链接</li>
                    <li>与用户分享此链接 - 他们只需点击即可添加自己的IP</li>
                    <li>或使用上方按钮测试功能</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        function generateWhitelistURL() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入代理ID</p>
                    </div>
                \`;
                return;
            }

            const whitelistURL = \`\${window.location.origin}/api/whitelist/\${proxyId}?action=add\`;

            document.getElementById('result').innerHTML = \`
                <div class="result success">
                    <h3>🎉 白名单链接已生成！</h3>
                    <p><strong>📤 与用户分享此链接：</strong></p>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin: 15px 0; word-break: break-all;">
                        <a href="\${whitelistURL}" target="_blank" style="color: #155724; text-decoration: none; font-weight: 600;">\${whitelistURL}</a>
                        <button onclick="copyToClipboard('\${whitelistURL}', this)" class="copy-btn">📋 复制链接</button>
                    </div>
                    <p>✨ 用户只需点击此链接即可将自己的IP添加到白名单。</p>
                </div>
            \`;
        }

        async function addCurrentIP() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入代理ID</p>
                    </div>
                \`;
                return;
            }

            // 显示加载状态
            document.getElementById('result').innerHTML = \`
                <div class="result info">
                    <h3>⏳ 正在添加IP...</h3>
                    <p>请稍候，正在处理您的请求</p>
                </div>
            \`;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}?action=add\`, {
                    method: 'GET'
                });

                // 如果返回HTML，说明成功了
                if (response.headers.get('content-type').includes('text/html')) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 添加成功！</h3>
                            <p>您的IP已成功添加到白名单！</p>
                            <p>💡 您可以直接访问代理地址或查看详细结果页面。</p>
                            <div style="margin-top: 15px;">
                                <a href="/api/whitelist/\${proxyId}?action=add" target="_blank" class="copy-btn">🔗 查看详细结果</a>
                            </div>
                        </div>
                    \`;
                } else {
                    const result = await response.json();

                    if (result.success) {
                        document.getElementById('result').innerHTML = \`
                            <div class="result success">
                                <h3>✅ 添加成功！</h3>
                                <p><strong>📝 消息：</strong> \${result.message}</p>
                                <p><strong>🌐 您的IP：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.ip}</code></p>
                                <p><strong>🆔 代理ID：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.proxy_id}</code></p>
                                <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            </div>
                        \`;
                    } else {
                        document.getElementById('result').innerHTML = \`
                            <div class="result error">
                                <h3>❌ 添加失败</h3>
                                <p>错误详情：\${result.error}</p>
                                <p>💡 请检查代理ID是否正确，或稍后重试。</p>
                            </div>
                        \`;
                    }
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>添加IP失败：\${error.message}</p>
                        <p>💡 请检查网络连接或稍后重试。</p>
                    </div>
                \`;
            }
        }

        async function checkWhitelistStatus() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入代理ID</p>
                    </div>
                \`;
                return;
            }

            // 显示加载状态
            document.getElementById('result').innerHTML = \`
                <div class="result info">
                    <h3>🔍 正在查询...</h3>
                    <p>请稍候，正在检查白名单状态</p>
                </div>
            \`;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 查询失败</h3>
                            <p>错误详情：\${result.error}</p>
                            <p>💡 请检查代理ID是否正确。</p>
                        </div>
                    \`;
                } else {
                    const statusIcon = result.whitelisted ? '✅' : '❌';
                    const statusText = result.whitelisted ? '已在白名单中' : '不在白名单中';
                    const statusClass = result.whitelisted ? 'success' : 'error';

                    document.getElementById('result').innerHTML = \`
                        <div class="result \${statusClass}">
                            <h3>📋 白名单状态查询结果</h3>
                            <p><strong>🔍 状态：</strong> \${statusIcon} \${statusText}</p>
                            <p><strong>🌐 您的IP：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.ip}</code></p>
                            <p><strong>🆔 代理ID：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.proxy_id}</code></p>
                            <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            <div style="margin-top: 15px;">
                                <strong>📝 所有白名单IP：</strong>
                                <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin-top: 10px; max-height: 200px; overflow-y: auto;">
                                    \${result.all_ips.length > 0 ?
                                        result.all_ips.map((ip, index) => \`
                                            <div style="padding: 5px 0; border-bottom: 1px solid rgba(0,0,0,0.1); font-family: monospace;">
                                                <span style="color: #6c757d;">\${index + 1}.</span> \${ip}
                                                \${ip === result.ip ? '<span style="color: #28a745; font-weight: bold;"> (您的IP)</span>' : ''}
                                            </div>
                                        \`).join('')
                                        : '<div style="color: #6c757d; font-style: italic;">暂无白名单IP</div>'
                                    }
                                </div>
                            </div>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>检查状态失败：\${error.message}</p>
                        <p>💡 请检查网络连接或稍后重试。</p>
                    </div>
                \`;
            }
        }
    </script>
</body>
</html>`;
}

// 生成状态检查页面HTML
function getStatusHTML(proxyId, clientIP, isWhitelisted, ipList) {
  const statusIcon = isWhitelisted ? '✅' : '❌';
  const statusText = isWhitelisted ? '已在白名单中' : '不在白名单中';
  const statusColor = isWhitelisted ? '#28a745' : '#dc3545';

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白名单状态检查 - ${proxyId}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid ${statusColor};
        }

        .status-icon {
            font-size: 4em;
            margin-bottom: 15px;
        }

        .status-title {
            font-size: 1.8em;
            color: ${statusColor};
            font-weight: 600;
            margin-bottom: 20px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .info-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
            word-break: break-all;
            font-size: 0.95em;
        }

        .ip-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
        }

        .ip-list h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .ip-item {
            background: white;
            padding: 12px 15px;
            margin: 8px 0;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: 'Courier New', monospace;
        }

        .ip-item.current {
            background: #d4edda;
            border-color: #c3e6cb;
            font-weight: 600;
        }

        .current-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        .refresh-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #1565c0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 白名单状态检查</h1>
            <p>实时查看您的IP白名单状态</p>
        </div>

        <div class="status-card">
            <div class="status-icon">${statusIcon}</div>
            <div class="status-title">${statusText}</div>

            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">🌐 您的IP地址</div>
                    <div class="info-value">${clientIP}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">🆔 代理ID</div>
                    <div class="info-value">${proxyId}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">📊 白名单总数</div>
                    <div class="info-value">${ipList.length} 个IP</div>
                </div>
                <div class="info-item">
                    <div class="info-label">⏰ 检查时间</div>
                    <div class="info-value" id="checkTime">${new Date().toLocaleString('zh-CN')}</div>
                </div>
            </div>
        </div>

        ${ipList.length > 0 ? `
        <div class="ip-list">
            <h3>📝 白名单IP列表</h3>
            ${ipList.map((ip, index) => `
                <div class="ip-item ${ip === clientIP ? 'current' : ''}">
                    <span>${index + 1}. ${ip}</span>
                    ${ip === clientIP ? '<span class="current-badge">当前IP</span>' : ''}
                </div>
            `).join('')}
        </div>
        ` : `
        <div class="ip-list">
            <h3>📝 白名单IP列表</h3>
            <div style="color: #6c757d; font-style: italic; text-align: center; padding: 20px;">
                暂无白名单IP
            </div>
        </div>
        `}

        <div class="refresh-info">
            💡 <strong>提示：</strong> 点击"刷新状态"可获取最新的白名单信息
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="refreshStatus()">
                🔄 刷新状态
            </button>
            ${!isWhitelisted ? `
            <a href="/api/whitelist/${proxyId}?action=add" class="btn btn-success">
                ➕ 添加我的IP
            </a>
            ` : ''}
            <a href="/whitelist-helper" class="btn btn-secondary">
                🛠️ 白名单工具
            </a>
        </div>
    </div>

    <script>
        function refreshStatus() {
            const container = document.querySelector('.container');
            container.classList.add('loading');

            // 更新检查时间
            document.getElementById('checkTime').textContent = '正在刷新...';

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 500);
        }

        // 自动刷新功能（可选）
        let autoRefresh = false;
        let refreshInterval;

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            if (autoRefresh) {
                refreshInterval = setInterval(refreshStatus, 30000); // 30秒刷新一次
                console.log('自动刷新已启用');
            } else {
                clearInterval(refreshInterval);
                console.log('自动刷新已禁用');
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                refreshStatus();
            }
        });
    </script>
</body>
</html>`;
}

// 生成状态检查错误页面HTML
function getStatusErrorHTML(proxyId, clientIP, errorMessage) {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>状态检查错误 - ${proxyId}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .error-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .error-title {
            color: #dc3545;
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">状态检查失败</div>

        <div class="error-message">
            <strong>错误详情：</strong> ${errorMessage}
        </div>

        <div class="info-item">
            <div class="info-label">🆔 代理ID</div>
            <div class="info-value">${proxyId}</div>
        </div>

        <div class="info-item">
            <div class="info-label">🌐 您的IP地址</div>
            <div class="info-value">${clientIP}</div>
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="window.location.reload()">
                🔄 重试
            </button>
            <a href="/whitelist-helper" class="btn btn-secondary">
                🛠️ 白名单工具
            </a>
        </div>
    </div>
</body>
</html>`;
}

// 生成代理不存在页面HTML
function getStatusNotFoundHTML(proxyId) {
  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理未找到 - ${proxyId}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .not-found-icon {
            font-size: 4em;
            margin-bottom: 20px;
        }

        .not-found-title {
            color: #dc3545;
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .not-found-message {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #ffeaa7;
        }

        .info-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: left;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .info-value {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            word-break: break-all;
        }

        .actions {
            margin-top: 30px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="not-found-icon">🔍</div>
        <div class="not-found-title">代理未找到</div>

        <div class="not-found-message">
            <strong>抱歉！</strong> 指定的代理ID不存在或已被删除。
        </div>

        <div class="info-item">
            <div class="info-label">🆔 查询的代理ID</div>
            <div class="info-value">${proxyId}</div>
        </div>

        <div class="actions">
            <a href="/" class="btn btn-primary">
                🏠 首页
            </a>
            <a href="/whitelist-helper" class="btn btn-secondary">
                🛠️ 白名单工具
            </a>
        </div>
    </div>
</body>
</html>`;
}
