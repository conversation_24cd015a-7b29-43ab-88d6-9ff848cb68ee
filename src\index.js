// Cloudflare Workers 订阅代理服务
// 支持IP白名单和订阅地址管理

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);
    const path = url.pathname;
    
    // 获取客户端IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    request.headers.get('X-Real-IP') || 
                    '127.0.0.1';

    // 路由处理
    if (path === '/admin') {
      return handleAdmin(request, env);
    } else if (path.startsWith('/api/')) {
      return handleAPI(request, env, clientIP);
    } else if (path.startsWith('/proxy/')) {
      return handleProxy(request, env, clientIP);
    } else if (path === '/whitelist-helper') {
      return handleWhitelistHelper(request);
    } else if (path === '/') {
      return new Response('订阅代理服务', { status: 200 });
    }
    
    return new Response('页面未找到', { status: 404 });
  }
};

// 处理管理页面
async function handleAdmin(request, env) {
  if (request.method === 'GET') {
    return new Response(getAdminHTML(), {
      headers: { 'Content-Type': 'text/html' }
    });
  }
  
  if (request.method === 'POST') {
    const formData = await request.formData();
    const password = formData.get('password');
    
    if (password !== env.ADMIN_PASSWORD) {
      return new Response('未授权访问', { status: 401 });
    }
    
    const action = formData.get('action');
    
    if (action === 'add_subscription') {
      const originalUrl = formData.get('original_url');
      const proxyId = generateId();
      
      await env.SUBSCRIPTION_KV.put(`sub:${proxyId}`, originalUrl);
      
      const proxyUrl = `${new URL(request.url).origin}/proxy/${proxyId}`;
      const whitelistUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}?action=add`;
      const checkUrl = `${new URL(request.url).origin}/api/whitelist/${proxyId}`;

      return new Response(JSON.stringify({
        success: true,
        proxy_url: proxyUrl,
        whitelist_url: whitelistUrl,
        check_url: checkUrl,
        proxy_id: proxyId
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }
    
    if (action === 'add_ip') {
      const proxyId = formData.get('proxy_id');
      const ip = formData.get('ip');

      // 验证输入
      if (!proxyId || !ip) {
        return new Response(JSON.stringify({
          success: false,
          error: '缺少代理ID或IP地址'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(ip)) {
          ipList.push(ip);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        return new Response(JSON.stringify({
          success: true,
          message: `IP ${ip} 已成功添加到代理 ${proxyId} 的白名单`,
          ip: ip,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          success: false,
          error: `添加IP失败: ${error.message}`
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('不允许的请求方法', { status: 405 });
}

// 处理API请求
async function handleAPI(request, env, clientIP) {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  
  if (pathParts[2] === 'whitelist' && pathParts[3]) {
    const proxyId = pathParts[3];
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (request.method === 'POST' || action === 'add') {
      // 添加当前IP到白名单
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        if (!ipList.includes(clientIP)) {
          ipList.push(clientIP);
          await env.SUBSCRIPTION_KV.put(`whitelist:${proxyId}`, JSON.stringify(ipList));
        }

        // 如果是GET请求且action=add，返回用户友好的HTML页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPSuccessHTML(clientIP, proxyId, ipList.length), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        // 否则返回JSON响应
        return new Response(JSON.stringify({
          success: true,
          message: `IP ${clientIP} 已成功添加到代理 ${proxyId} 的白名单`,
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        // 如果是GET请求且action=add，返回用户友好的错误页面
        if (request.method === 'GET' && action === 'add') {
          return new Response(getAddIPErrorHTML(clientIP, proxyId, error.message), {
            headers: { 'Content-Type': 'text/html' }
          });
        }

        return new Response(JSON.stringify({
          success: false,
          error: `添加IP到白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }

    if (request.method === 'GET' && action !== 'add') {
      // 检查IP是否在白名单中
      try {
        const existingIPs = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
        const ipList = existingIPs ? JSON.parse(existingIPs) : [];

        return new Response(JSON.stringify({
          whitelisted: ipList.includes(clientIP),
          ip: clientIP,
          proxy_id: proxyId,
          total_ips: ipList.length,
          all_ips: ipList // 显示所有白名单IP用于调试
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      } catch (error) {
        return new Response(JSON.stringify({
          error: `检查白名单失败: ${error.message}`,
          ip: clientIP,
          proxy_id: proxyId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }
    }
  }
  
  return new Response('API接口未找到', { status: 404 });
}

// 处理代理请求
async function handleProxy(request, env, clientIP) {
  const url = new URL(request.url);
  const proxyId = url.pathname.split('/')[2];
  
  if (!proxyId) {
    return new Response('无效的代理ID', { status: 400 });
  }
  
  // 检查IP白名单
  const whitelistData = await env.SUBSCRIPTION_KV.get(`whitelist:${proxyId}`);
  const whitelist = whitelistData ? JSON.parse(whitelistData) : [];
  
  if (!whitelist.includes(clientIP)) {
    return new Response('访问被拒绝：IP不在白名单中', { status: 403 });
  }
  
  // 获取原始订阅地址
  const originalUrl = await env.SUBSCRIPTION_KV.get(`sub:${proxyId}`);
  
  if (!originalUrl) {
    return new Response('订阅地址未找到', { status: 404 });
  }
  
  try {
    // 代理请求到原始地址
    const response = await fetch(originalUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    // 返回代理响应
    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  } catch (error) {
    return new Response('代理错误: ' + error.message, { status: 500 });
  }
}

// 处理白名单助手页面
async function handleWhitelistHelper(request) {
  return new Response(getWhitelistHelperHTML(), {
    headers: { 'Content-Type': 'text/html' }
  });
}

// 生成随机ID
function generateId() {
  return Math.random().toString(36).substring(2, 15) +
         Math.random().toString(36).substring(2, 15);
}

// 获取管理页面HTML
function getAdminHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>订阅代理管理后台</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5em;
        }

        .card-icon.add { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .card-icon.ip { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .card-icon.view { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }

        .card h2 {
            color: #333;
            font-size: 1.5em;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 0.95em;
        }

        .input-wrapper {
            position: relative;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result h3 {
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .result p {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .result a {
            color: inherit;
            text-decoration: none;
            font-weight: 600;
            border-bottom: 1px dashed currentColor;
        }

        .result a:hover {
            border-bottom-style: solid;
        }

        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
        }

        .info-box strong {
            color: #1976d2;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 订阅代理管理后台</h1>
            <p>高效管理您的订阅代理服务和IP白名单</p>
        </div>

        <div class="content">
            <div class="card">
                <div class="card-header">
                    <div class="card-icon add">➕</div>
                    <h2>添加新订阅</h2>
                </div>
                <form id="addSubscriptionForm">
                    <div class="form-group">
                        <label>🔐 管理员密码</label>
                        <input type="password" id="password" placeholder="请输入管理员密码" required>
                    </div>
                    <div class="form-group">
                        <label>🔗 原始订阅地址</label>
                        <input type="url" id="originalUrl" placeholder="https://example.com/subscribe/your-subscription" required>
                    </div>
                    <button type="submit" class="btn">创建代理</button>
                </form>
                <div id="subscriptionResult"></div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon ip">🌐</div>
                    <h2>添加IP到白名单</h2>
                </div>
                <form id="addIPForm">
                    <div class="form-group">
                        <label>🔐 管理员密码</label>
                        <input type="password" id="ipPassword" placeholder="请输入管理员密码" required>
                    </div>
                    <div class="form-group">
                        <label>🆔 代理ID</label>
                        <input type="text" id="proxyId" placeholder="请输入代理ID" required>
                    </div>
                    <div class="form-group">
                        <label>📍 IP地址</label>
                        <input type="text" id="ipAddress" placeholder="例如: ***********" required>
                    </div>
                    <button type="submit" class="btn">添加IP</button>
                </form>
                <div id="ipResult"></div>
            </div>

            <div class="card">
                <div class="card-header">
                    <div class="card-icon view">👁️</div>
                    <h2>查看白名单</h2>
                </div>
                <form id="viewWhitelistForm">
                    <div class="form-group">
                        <label>🆔 代理ID</label>
                        <input type="text" id="viewProxyId" placeholder="请输入要查看的代理ID" required>
                    </div>
                    <button type="submit" class="btn">查看白名单</button>
                </form>
                <div id="whitelistResult"></div>
            </div>
        </div>
    </div>

    <script>
        // 添加加载动画
        function showLoading(buttonElement) {
            const originalText = buttonElement.textContent;
            buttonElement.textContent = '处理中...';
            buttonElement.disabled = true;
            return originalText;
        }

        function hideLoading(buttonElement, originalText) {
            buttonElement.textContent = originalText;
            buttonElement.disabled = false;
        }

        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        document.getElementById('addSubscriptionForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'add_subscription');
            formData.append('password', document.getElementById('password').value);
            formData.append('original_url', document.getElementById('originalUrl').value);

            try {
                const response = await fetch('/admin', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('subscriptionResult').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 代理创建成功！</h3>
                            <p><strong>🔗 代理地址：</strong> <a href="\${result.proxy_url}" target="_blank">\${result.proxy_url}</a>
                               <button onclick="copyToClipboard('\${result.proxy_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>✨ 白名单地址（用户使用）：</strong> <a href="\${result.whitelist_url}" target="_blank">\${result.whitelist_url}</a>
                               <button onclick="copyToClipboard('\${result.whitelist_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>📊 状态检查地址：</strong> <a href="\${result.check_url}" target="_blank">\${result.check_url}</a>
                               <button onclick="copyToClipboard('\${result.check_url}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <p><strong>🆔 代理ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code>
                               <button onclick="copyToClipboard('\${result.proxy_id}', this)" style="margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">复制</button></p>
                            <div class="info-box">
                                <strong>📋 分享给用户：</strong><br>
                                用户只需点击上方的"白名单地址"即可将自己的IP添加到白名单。<br>
                                他们无需任何额外操作，点击后即可自动添加。
                            </div>
                        </div>
                    \`;
                } else {
                    document.getElementById('subscriptionResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 创建代理失败</h3>
                            <p>请检查您的密码是否正确，或稍后重试。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('subscriptionResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        document.getElementById('addIPForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);

            const formData = new FormData();
            formData.append('action', 'add_ip');
            formData.append('password', document.getElementById('ipPassword').value);
            formData.append('proxy_id', document.getElementById('proxyId').value);
            formData.append('ip', document.getElementById('ipAddress').value);

            try {
                const response = await fetch('/admin', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    document.getElementById('ipResult').innerHTML = \`
                        <div class="result success">
                            <h3>✅ IP添加成功！</h3>
                            <p><strong>📍 IP地址：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.ip}</code></p>
                            <p><strong>🆔 代理ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code></p>
                            <p>该IP现在可以访问对应的代理地址了。</p>
                        </div>
                    \`;
                } else {
                    document.getElementById('ipResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 添加IP失败</h3>
                            <p>错误详情：\${result.error || '未知错误'}</p>
                            <p>请检查您的密码和输入信息是否正确。</p>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('ipResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });

        document.getElementById('viewWhitelistForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = showLoading(submitBtn);
            const proxyId = document.getElementById('viewProxyId').value;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('whitelistResult').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 查询失败</h3>
                            <p>错误详情：\${result.error}</p>
                            <p>请检查代理ID是否正确。</p>
                        </div>
                    \`;
                } else {
                    const statusIcon = result.whitelisted ? '✅' : '❌';
                    const statusText = result.whitelisted ? '是' : '否';
                    const statusClass = result.whitelisted ? 'success' : 'error';

                    document.getElementById('whitelistResult').innerHTML = \`
                        <div class="result \${statusClass}">
                            <h3>📋 代理ID白名单详情</h3>
                            <p><strong>🆔 代理ID：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.proxy_id}</code></p>
                            <p><strong>🌐 您的IP：</strong> <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 4px; font-family: monospace;">\${result.ip}</code></p>
                            <p><strong>🔍 是否在白名单中：</strong> \${statusIcon} \${statusText}</p>
                            <p><strong>📊 白名单IP总数：</strong> \${result.total_ips}</p>
                            <div style="margin-top: 15px;">
                                <strong>📝 所有白名单IP：</strong>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 10px; max-height: 200px; overflow-y: auto;">
                                    \${result.all_ips.length > 0 ?
                                        result.all_ips.map((ip, index) => \`
                                            <div style="padding: 5px 0; border-bottom: 1px solid #e9ecef; font-family: monospace;">
                                                <span style="color: #6c757d;">\${index + 1}.</span> \${ip}
                                                \${ip === result.ip ? '<span style="color: #28a745; font-weight: bold;"> (您的IP)</span>' : ''}
                                            </div>
                                        \`).join('')
                                        : '<div style="color: #6c757d; font-style: italic;">暂无白名单IP</div>'
                                    }
                                </div>
                            </div>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('whitelistResult').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>错误详情：\${error.message}</p>
                    </div>
                \`;
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });
    </script>
</body>
</html>`;
}

// 获取添加IP成功页面HTML
function getAddIPSuccessHTML(ip, proxyId, totalIPs) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>IP添加成功</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            animation: slideUp 0.6s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .info-card {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #28a745;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #28a745;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #155724;
            min-width: 120px;
        }

        .info-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }

        .proxy-link {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }

        .proxy-link a {
            color: #1976d2;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
        }

        .proxy-link a:hover {
            text-decoration: underline;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .info-label {
                margin-bottom: 5px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 IP添加成功！</h1>
        </div>

        <div class="content">
            <div class="info-card">
                <div class="info-item">
                    <span class="info-label">📍 您的IP地址：</span>
                    <span class="info-value">${ip}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">🆔 代理ID：</span>
                    <span class="info-value">${proxyId}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">📊 白名单总数：</span>
                    <span class="info-value">${totalIPs} 个IP</span>
                </div>
            </div>

            <div class="proxy-link">
                <p style="margin-bottom: 10px; color: #1976d2; font-weight: 600;">🚀 您现在可以访问代理地址：</p>
                <a href="/proxy/${proxyId}" target="_blank">/proxy/${proxyId}</a>
            </div>

            <div class="button-group">
                <a href="/api/whitelist/${proxyId}" class="btn">📋 查看白名单状态</a>
                <a href="/whitelist-helper" class="btn secondary">🛠️ 白名单助手</a>
            </div>
        </div>
    </div>
</body>
</html>`;
}

// 获取添加IP错误页面HTML
function getAddIPErrorHTML(ip, proxyId, errorMessage) {
  return `<!DOCTYPE html>
<html>
<head>
    <title>添加IP错误</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            max-width: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
            animation: slideUp 0.6s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .error-card {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border: 1px solid #dc3545;
            border-radius: 15px;
            padding: 25px;
            margin: 25px 0;
            border-left: 4px solid #dc3545;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 8px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .info-label {
            font-weight: 600;
            color: #721c24;
            min-width: 120px;
        }

        .info-value {
            font-family: monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            color: #495057;
        }

        .error-message {
            background: rgba(220, 53, 69, 0.1);
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
            font-weight: 500;
        }

        .help-text {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            color: #856404;
            text-align: center;
        }

        .button-group {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.retry {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.retry:hover {
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(108, 117, 125, 0.4);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 1.8em;
            }

            .content {
                padding: 20px;
            }

            .info-item {
                flex-direction: column;
                align-items: flex-start;
            }

            .info-label {
                margin-bottom: 5px;
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>❌ 添加IP失败</h1>
        </div>

        <div class="content">
            <div class="error-card">
                <div class="info-item">
                    <span class="info-label">📍 您的IP地址：</span>
                    <span class="info-value">${ip}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">🆔 代理ID：</span>
                    <span class="info-value">${proxyId}</span>
                </div>
            </div>

            <div class="error-message">
                <strong>🚨 错误详情：</strong> ${errorMessage}
            </div>

            <div class="help-text">
                <p><strong>💡 建议解决方案：</strong></p>
                <p>• 检查代理ID是否正确</p>
                <p>• 稍后重试</p>
                <p>• 联系管理员获取帮助</p>
            </div>

            <div class="button-group">
                <a href="/api/whitelist/${proxyId}?action=add" class="btn retry">🔄 重试</a>
                <a href="/whitelist-helper" class="btn secondary">🛠️ 白名单助手</a>
            </div>
        </div>
    </div>
</body>
</html>`;
}

// 获取白名单助手页面HTML
function getWhitelistHelperHTML() {
  return `<!DOCTYPE html>
<html>
<head>
    <title>白名单助手</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .input-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
            font-size: 1.1em;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1em;
            transition: all 0.3s ease;
            background: #fafbfc;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4facfe;
            background: white;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
        }

        .btn {
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            text-decoration: none;
            display: inline-block;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn.primary {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn.primary:hover {
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .btn.secondary:hover {
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn.info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        }

        .btn.info:hover {
            box-shadow: 0 10px 25px rgba(23, 162, 184, 0.4);
        }

        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .result.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-left-color: #28a745;
            color: #155724;
        }

        .result.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-left-color: #dc3545;
            color: #721c24;
        }

        .result.info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-left-color: #17a2b8;
            color: #0c5460;
        }

        .info-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            border: 1px solid #2196f3;
            border-radius: 15px;
            padding: 30px;
            margin-top: 30px;
        }

        .info-section h3 {
            color: #1976d2;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-section p {
            margin-bottom: 10px;
            line-height: 1.6;
            color: #1976d2;
        }

        .info-section code {
            background: rgba(25, 118, 210, 0.1);
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            color: #1565c0;
        }

        .info-section ul {
            margin-left: 20px;
            color: #1976d2;
        }

        .info-section li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .copy-btn {
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .copy-btn:hover {
            background: #218838;
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }

            .header {
                padding: 30px 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .input-card {
                padding: 20px;
            }

            .button-group {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛠️ 白名单助手</h1>
            <p>轻松管理您的IP白名单，一键添加或查看状态</p>
        </div>

        <div class="content">
            <div class="input-card">
                <div class="form-group">
                    <label>🆔 代理ID</label>
                    <input type="text" id="proxyId" placeholder="请输入您的代理ID" required>
                </div>

                <div class="button-group">
                    <button onclick="addCurrentIP()" class="btn primary">➕ 将我的IP添加到白名单</button>
                    <button onclick="checkWhitelistStatus()" class="btn secondary">🔍 检查白名单状态</button>
                    <button onclick="generateWhitelistURL()" class="btn info">🔗 生成白名单链接</button>
                </div>

                <div id="result"></div>
            </div>

            <div class="info-section">
                <h3>🚀 简单方法 - 直接访问链接</h3>
                <p>您也可以通过在浏览器中访问此链接来添加您的IP：</p>
                <p><code>/api/whitelist/您的代理ID?action=add</code></p>
                <p>只需将"您的代理ID"替换为您的实际代理ID。</p>

                <h3>📖 使用说明</h3>
                <ul>
                    <li>在上方字段中输入您的代理ID</li>
                    <li>点击"生成白名单链接"获取直接链接</li>
                    <li>与用户分享此链接 - 他们只需点击即可添加自己的IP</li>
                    <li>或使用上方按钮测试功能</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 复制到剪贴板功能
        function copyToClipboard(text, buttonElement) {
            navigator.clipboard.writeText(text).then(function() {
                const originalText = buttonElement.textContent;
                buttonElement.textContent = '✅ 已复制';
                buttonElement.style.background = '#28a745';
                setTimeout(() => {
                    buttonElement.textContent = originalText;
                    buttonElement.style.background = '';
                }, 2000);
            }, function(err) {
                console.error('无法复制文本: ', err);
                alert('复制失败，请手动复制');
            });
        }

        function generateWhitelistURL() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入代理ID</p>
                    </div>
                \`;
                return;
            }

            const whitelistURL = \`\${window.location.origin}/api/whitelist/\${proxyId}?action=add\`;

            document.getElementById('result').innerHTML = \`
                <div class="result success">
                    <h3>🎉 白名单链接已生成！</h3>
                    <p><strong>📤 与用户分享此链接：</strong></p>
                    <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin: 15px 0; word-break: break-all;">
                        <a href="\${whitelistURL}" target="_blank" style="color: #155724; text-decoration: none; font-weight: 600;">\${whitelistURL}</a>
                        <button onclick="copyToClipboard('\${whitelistURL}', this)" class="copy-btn">📋 复制链接</button>
                    </div>
                    <p>✨ 用户只需点击此链接即可将自己的IP添加到白名单。</p>
                </div>
            \`;
        }

        async function addCurrentIP() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入代理ID</p>
                    </div>
                \`;
                return;
            }

            // 显示加载状态
            document.getElementById('result').innerHTML = \`
                <div class="result info">
                    <h3>⏳ 正在添加IP...</h3>
                    <p>请稍候，正在处理您的请求</p>
                </div>
            \`;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}?action=add\`, {
                    method: 'GET'
                });

                // 如果返回HTML，说明成功了
                if (response.headers.get('content-type').includes('text/html')) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result success">
                            <h3>🎉 添加成功！</h3>
                            <p>您的IP已成功添加到白名单！</p>
                            <p>💡 您可以直接访问代理地址或查看详细结果页面。</p>
                            <div style="margin-top: 15px;">
                                <a href="/api/whitelist/\${proxyId}?action=add" target="_blank" class="copy-btn">🔗 查看详细结果</a>
                            </div>
                        </div>
                    \`;
                } else {
                    const result = await response.json();

                    if (result.success) {
                        document.getElementById('result').innerHTML = \`
                            <div class="result success">
                                <h3>✅ 添加成功！</h3>
                                <p><strong>📝 消息：</strong> \${result.message}</p>
                                <p><strong>🌐 您的IP：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.ip}</code></p>
                                <p><strong>🆔 代理ID：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.proxy_id}</code></p>
                                <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            </div>
                        \`;
                    } else {
                        document.getElementById('result').innerHTML = \`
                            <div class="result error">
                                <h3>❌ 添加失败</h3>
                                <p>错误详情：\${result.error}</p>
                                <p>💡 请检查代理ID是否正确，或稍后重试。</p>
                            </div>
                        \`;
                    }
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>添加IP失败：\${error.message}</p>
                        <p>💡 请检查网络连接或稍后重试。</p>
                    </div>
                \`;
            }
        }

        async function checkWhitelistStatus() {
            const proxyId = document.getElementById('proxyId').value;

            if (!proxyId) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 输入错误</h3>
                        <p>请先输入代理ID</p>
                    </div>
                \`;
                return;
            }

            // 显示加载状态
            document.getElementById('result').innerHTML = \`
                <div class="result info">
                    <h3>🔍 正在查询...</h3>
                    <p>请稍候，正在检查白名单状态</p>
                </div>
            \`;

            try {
                const response = await fetch(\`/api/whitelist/\${proxyId}\`, {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.error) {
                    document.getElementById('result').innerHTML = \`
                        <div class="result error">
                            <h3>❌ 查询失败</h3>
                            <p>错误详情：\${result.error}</p>
                            <p>💡 请检查代理ID是否正确。</p>
                        </div>
                    \`;
                } else {
                    const statusIcon = result.whitelisted ? '✅' : '❌';
                    const statusText = result.whitelisted ? '已在白名单中' : '不在白名单中';
                    const statusClass = result.whitelisted ? 'success' : 'error';

                    document.getElementById('result').innerHTML = \`
                        <div class="result \${statusClass}">
                            <h3>📋 白名单状态查询结果</h3>
                            <p><strong>🔍 状态：</strong> \${statusIcon} \${statusText}</p>
                            <p><strong>🌐 您的IP：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.ip}</code></p>
                            <p><strong>🆔 代理ID：</strong> <code style="background: rgba(255,255,255,0.7); padding: 2px 6px; border-radius: 4px;">\${result.proxy_id}</code></p>
                            <p><strong>📊 白名单总数：</strong> \${result.total_ips} 个IP</p>
                            <div style="margin-top: 15px;">
                                <strong>📝 所有白名单IP：</strong>
                                <div style="background: rgba(255,255,255,0.7); padding: 15px; border-radius: 8px; margin-top: 10px; max-height: 200px; overflow-y: auto;">
                                    \${result.all_ips.length > 0 ?
                                        result.all_ips.map((ip, index) => \`
                                            <div style="padding: 5px 0; border-bottom: 1px solid rgba(0,0,0,0.1); font-family: monospace;">
                                                <span style="color: #6c757d;">\${index + 1}.</span> \${ip}
                                                \${ip === result.ip ? '<span style="color: #28a745; font-weight: bold;"> (您的IP)</span>' : ''}
                                            </div>
                                        \`).join('')
                                        : '<div style="color: #6c757d; font-style: italic;">暂无白名单IP</div>'
                                    }
                                </div>
                            </div>
                        </div>
                    \`;
                }
            } catch (error) {
                document.getElementById('result').innerHTML = \`
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p>检查状态失败：\${error.message}</p>
                        <p>💡 请检查网络连接或稍后重试。</p>
                    </div>
                \`;
            }
        }
    </script>
</body>
</html>`;
}
